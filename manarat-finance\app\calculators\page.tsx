'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '../../components/layout/header'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import {
  Calculator,
  TrendingUp,
  PiggyBank,
  CreditCard,
  Building,
  Percent,
  DollarSign,
  BarChart3
} from 'lucide-react'
import { formatCurrency, formatArabicNumber, formatPercentage } from '../../lib/utils'

// Mock user data
const mockUser = {
  name: 'أحمد محمد',
  level: 3,
  xp: 2150,
  avatar: '/avatar.jpg'
}

interface CalculatorResult {
  title: string
  value: number
  description: string
  color: string
}

export default function CalculatorsPage() {
  // Compound Interest Calculator State
  const [compoundInterest, setCompoundInterest] = useState({
    principal: 100000,
    rate: 5,
    time: 10,
    compound: 12
  })

  // Loan Calculator State
  const [loanCalculator, setLoanCalculator] = useState({
    amount: 500000,
    rate: 8,
    years: 15
  })

  // Investment Calculator State
  const [investment, setInvestment] = useState({
    initialAmount: 50000,
    monthlyContribution: 5000,
    expectedReturn: 7,
    years: 20
  })

  // Financial Ratios State
  const [ratios, setRatios] = useState({
    currentAssets: 200000,
    currentLiabilities: 100000,
    totalDebt: 300000,
    totalEquity: 500000,
    netIncome: 80000,
    totalAssets: 800000
  })

  // Calculate Compound Interest
  const calculateCompoundInterest = () => {
    const { principal, rate, time, compound } = compoundInterest
    const amount = principal * Math.pow((1 + rate / 100 / compound), compound * time)
    const interest = amount - principal
    return { amount, interest }
  }

  // Calculate Loan Payment
  const calculateLoanPayment = () => {
    const { amount, rate, years } = loanCalculator
    const monthlyRate = rate / 100 / 12
    const numPayments = years * 12
    const monthlyPayment = (amount * monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
                          (Math.pow(1 + monthlyRate, numPayments) - 1)
    const totalPayment = monthlyPayment * numPayments
    const totalInterest = totalPayment - amount
    return { monthlyPayment, totalPayment, totalInterest }
  }

  // Calculate Investment Growth
  const calculateInvestmentGrowth = () => {
    const { initialAmount, monthlyContribution, expectedReturn, years } = investment
    const monthlyRate = expectedReturn / 100 / 12
    const months = years * 12

    // Future value of initial amount
    const futureValueInitial = initialAmount * Math.pow(1 + monthlyRate, months)

    // Future value of monthly contributions
    const futureValueContributions = monthlyContribution *
      ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate)

    const totalValue = futureValueInitial + futureValueContributions
    const totalContributions = initialAmount + (monthlyContribution * months)
    const totalGains = totalValue - totalContributions

    return { totalValue, totalContributions, totalGains }
  }

  // Calculate Financial Ratios
  const calculateFinancialRatios = () => {
    const { currentAssets, currentLiabilities, totalDebt, totalEquity, netIncome, totalAssets } = ratios

    const currentRatio = currentAssets / currentLiabilities
    const debtToEquityRatio = totalDebt / totalEquity
    const returnOnAssets = (netIncome / totalAssets) * 100
    const debtRatio = (totalDebt / totalAssets) * 100

    return { currentRatio, debtToEquityRatio, returnOnAssets, debtRatio }
  }

  const compoundResult = calculateCompoundInterest()
  const loanResult = calculateLoanPayment()
  const investmentResult = calculateInvestmentGrowth()
  const ratiosResult = calculateFinancialRatios()

  const calculators = [
    {
      id: 'compound',
      title: 'حاسبة الفوائد المركبة',
      description: 'احسب نمو استثماراتك مع الفوائد المركبة',
      icon: TrendingUp,
      color: 'bg-blue-500'
    },
    {
      id: 'loan',
      title: 'حاسبة القروض',
      description: 'احسب الأقساط الشهرية والفوائد الإجمالية',
      icon: CreditCard,
      color: 'bg-green-500'
    },
    {
      id: 'investment',
      title: 'حاسبة الاستثمار',
      description: 'خطط لمستقبلك المالي مع الاستثمار المنتظم',
      icon: PiggyBank,
      color: 'bg-purple-500'
    },
    {
      id: 'ratios',
      title: 'النسب المالية',
      description: 'حلل الوضع المالي للشركات',
      icon: BarChart3,
      color: 'bg-orange-500'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Header user={mockUser} />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3">
            <Calculator className="h-8 w-8 text-primary-600" />
            الحاسبات المالية
          </h1>
          <p className="text-gray-600">
            أدوات تفاعلية لحساب وتحليل البيانات المالية المختلفة
          </p>
        </div>

        {/* Calculator Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

          {/* Compound Interest Calculator */}
          <Card className="card-hover">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                حاسبة الفوائد المركبة
              </CardTitle>
              <CardDescription>
                احسب نمو استثماراتك مع الفوائد المركبة
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">المبلغ الأساسي (دج)</label>
                  <input
                    type="number"
                    value={compoundInterest.principal}
                    onChange={(e) => setCompoundInterest({...compoundInterest, principal: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">معدل الفائدة السنوي (%)</label>
                  <input
                    type="number"
                    step="0.1"
                    value={compoundInterest.rate}
                    onChange={(e) => setCompoundInterest({...compoundInterest, rate: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">المدة (سنوات)</label>
                  <input
                    type="number"
                    value={compoundInterest.time}
                    onChange={(e) => setCompoundInterest({...compoundInterest, time: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">تكرار التركيب (سنوياً)</label>
                  <select
                    value={compoundInterest.compound}
                    onChange={(e) => setCompoundInterest({...compoundInterest, compound: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  >
                    <option value={1}>سنوياً</option>
                    <option value={4}>ربع سنوي</option>
                    <option value={12}>شهرياً</option>
                    <option value={365}>يومياً</option>
                  </select>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600 arabic-numbers">
                      {formatCurrency(compoundResult.amount, 'DZD')}
                    </div>
                    <div className="text-sm text-gray-600">المبلغ النهائي</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600 arabic-numbers">
                      {formatCurrency(compoundResult.interest, 'DZD')}
                    </div>
                    <div className="text-sm text-gray-600">إجمالي الفوائد</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Loan Calculator */}
          <Card className="card-hover">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-green-600" />
                حاسبة القروض
              </CardTitle>
              <CardDescription>
                احسب الأقساط الشهرية والفوائد الإجمالية
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">مبلغ القرض (دج)</label>
                  <input
                    type="number"
                    value={loanCalculator.amount}
                    onChange={(e) => setLoanCalculator({...loanCalculator, amount: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">معدل الفائدة السنوي (%)</label>
                    <input
                      type="number"
                      step="0.1"
                      value={loanCalculator.rate}
                      onChange={(e) => setLoanCalculator({...loanCalculator, rate: Number(e.target.value)})}
                      className="w-full p-2 border rounded-md text-right"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">مدة القرض (سنوات)</label>
                    <input
                      type="number"
                      value={loanCalculator.years}
                      onChange={(e) => setLoanCalculator({...loanCalculator, years: Number(e.target.value)})}
                      className="w-full p-2 border rounded-md text-right"
                    />
                  </div>
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">القسط الشهري:</span>
                  <span className="font-bold text-green-600 arabic-numbers">
                    {formatCurrency(loanResult.monthlyPayment, 'DZD')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">إجمالي المدفوعات:</span>
                  <span className="font-bold arabic-numbers">
                    {formatCurrency(loanResult.totalPayment, 'DZD')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">إجمالي الفوائد:</span>
                  <span className="font-bold text-red-600 arabic-numbers">
                    {formatCurrency(loanResult.totalInterest, 'DZD')}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Investment Calculator */}
          <Card className="card-hover">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PiggyBank className="h-5 w-5 text-purple-600" />
                حاسبة الاستثمار
              </CardTitle>
              <CardDescription>
                خطط لمستقبلك المالي مع الاستثمار المنتظم
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">المبلغ الأولي (دج)</label>
                  <input
                    type="number"
                    value={investment.initialAmount}
                    onChange={(e) => setInvestment({...investment, initialAmount: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">المساهمة الشهرية (دج)</label>
                  <input
                    type="number"
                    value={investment.monthlyContribution}
                    onChange={(e) => setInvestment({...investment, monthlyContribution: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">العائد المتوقع (%)</label>
                  <input
                    type="number"
                    step="0.1"
                    value={investment.expectedReturn}
                    onChange={(e) => setInvestment({...investment, expectedReturn: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">مدة الاستثمار (سنوات)</label>
                  <input
                    type="number"
                    value={investment.years}
                    onChange={(e) => setInvestment({...investment, years: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">القيمة النهائية:</span>
                  <span className="font-bold text-purple-600 arabic-numbers">
                    {formatCurrency(investmentResult.totalValue, 'DZD')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">إجمالي المساهمات:</span>
                  <span className="font-bold arabic-numbers">
                    {formatCurrency(investmentResult.totalContributions, 'DZD')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">إجمالي الأرباح:</span>
                  <span className="font-bold text-green-600 arabic-numbers">
                    {formatCurrency(investmentResult.totalGains, 'DZD')}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Financial Ratios Calculator */}
          <Card className="card-hover">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-orange-600" />
                النسب المالية
              </CardTitle>
              <CardDescription>
                حلل الوضع المالي للشركات
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">الأصول المتداولة (دج)</label>
                  <input
                    type="number"
                    value={ratios.currentAssets}
                    onChange={(e) => setRatios({...ratios, currentAssets: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">الخصوم المتداولة (دج)</label>
                  <input
                    type="number"
                    value={ratios.currentLiabilities}
                    onChange={(e) => setRatios({...ratios, currentLiabilities: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">إجمالي الديون (دج)</label>
                  <input
                    type="number"
                    value={ratios.totalDebt}
                    onChange={(e) => setRatios({...ratios, totalDebt: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">إجمالي حقوق الملكية (دج)</label>
                  <input
                    type="number"
                    value={ratios.totalEquity}
                    onChange={(e) => setRatios({...ratios, totalEquity: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">صافي الدخل (دج)</label>
                  <input
                    type="number"
                    value={ratios.netIncome}
                    onChange={(e) => setRatios({...ratios, netIncome: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">إجمالي الأصول (دج)</label>
                  <input
                    type="number"
                    value={ratios.totalAssets}
                    onChange={(e) => setRatios({...ratios, totalAssets: Number(e.target.value)})}
                    className="w-full p-2 border rounded-md text-right"
                  />
                </div>
              </div>

              <div className="bg-orange-50 p-4 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">نسبة السيولة:</span>
                  <span className="font-bold text-orange-600 arabic-numbers">
                    {ratiosResult.currentRatio.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">نسبة الدين إلى حقوق الملكية:</span>
                  <span className="font-bold arabic-numbers">
                    {ratiosResult.debtToEquityRatio.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">العائد على الأصول:</span>
                  <span className="font-bold text-green-600 arabic-numbers">
                    {formatPercentage(ratiosResult.returnOnAssets)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">نسبة الدين:</span>
                  <span className="font-bold arabic-numbers">
                    {formatPercentage(ratiosResult.debtRatio)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tips Section */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5 text-primary-600" />
              نصائح مالية مهمة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold text-blue-800 mb-2">الفوائد المركبة</h3>
                <p className="text-sm text-blue-700">
                  كلما بدأت الاستثمار مبكراً، كلما استفدت أكثر من قوة الفوائد المركبة
                </p>
              </div>
              <div className="p-4 bg-green-50 rounded-lg">
                <h3 className="font-semibold text-green-800 mb-2">إدارة الديون</h3>
                <p className="text-sm text-green-700">
                  حافظ على نسبة الدين إلى الدخل أقل من 40% لضمان الاستقرار المالي
                </p>
              </div>
              <div className="p-4 bg-purple-50 rounded-lg">
                <h3 className="font-semibold text-purple-800 mb-2">التنويع</h3>
                <p className="text-sm text-purple-700">
                  نوع استثماراتك لتقليل المخاطر وزيادة فرص العائد المستقر
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
