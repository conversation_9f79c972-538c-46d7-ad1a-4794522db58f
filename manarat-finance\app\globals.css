@import "tailwindcss";

/* Arabic Fonts Import */
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  /* منارة المحاسبة والمالية Theme Colors */
  --background: #F9FAFB;
  --foreground: #111827;
  --primary: #1E40AF;
  --secondary: #10B981;
  --accent: #F59E0B;
  --neutral: #6B7280;
  --border: #E5E7EB;
  --card: #FFFFFF;
  --card-foreground: #374151;
  --muted: #F3F4F6;
  --muted-foreground: #6B7280;
  --destructive: #EF4444;
  --destructive-foreground: #FFFFFF;
  --success: #10B981;
  --warning: #F59E0B;
  --info: #3B82F6;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0F172A;
    --foreground: #F1F5F9;
    --card: #1E293B;
    --card-foreground: #F1F5F9;
    --muted: #334155;
    --muted-foreground: #94A3B8;
    --border: #334155;
  }
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  direction: rtl; /* Arabic RTL by default */
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'IBM Plex Arabic', 'Tajawal', system-ui, sans-serif;
  font-feature-settings: 'kern' 1, 'liga' 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

/* Arabic Typography Enhancements */
.arabic-text {
  font-family: 'IBM Plex Arabic', 'Tajawal', system-ui, sans-serif;
  direction: rtl;
  text-align: right;
}

.latin-text {
  font-family: 'Inter', system-ui, sans-serif;
  direction: ltr;
  text-align: left;
}

.code-text {
  font-family: 'JetBrains Mono', monospace;
  direction: ltr;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--neutral);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Selection */
::selection {
  background: var(--primary);
  color: white;
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceGentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

/* Utility classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-bounce-gentle {
  animation: bounceGentle 2s infinite;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Arabic number formatting */
.arabic-numbers {
  font-variant-numeric: lining-nums;
  direction: ltr;
  display: inline-block;
}
