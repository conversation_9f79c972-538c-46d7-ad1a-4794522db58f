import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "منارة المحاسبة والمالية - منصة تعليمية ذكية",
  description: "منصة تعليمية تفاعلية متخصصة في المحاسبة والمالية للطلاب الجزائريين",
  keywords: "محاسبة، مالية، تعليم، الجزائر، SCF، تحليل مالي، تمويل إسلامي",
  authors: [{ name: "منارة المحاسبة والمالية" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
  openGraph: {
    title: "منارة المحاسبة والمالية",
    description: "منصة تعليمية تفاعلية متخصصة في المحاسبة والمالية",
    type: "website",
    locale: "ar_DZ",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <meta name="theme-color" content="#1E40AF" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className="font-arabic antialiased">
        {children}
      </body>
    </html>
  );
}
