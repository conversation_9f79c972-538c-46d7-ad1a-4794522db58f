'use client'

import React, { useState } from 'react'
import { Header } from '../../components/layout/header'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import {
  Users,
  MessageCircle,
  Heart,
  Share2,
  Plus,
  Search,
  Filter,
  Clock,
  Eye,
  ThumbsUp,
  BookOpen,
  HelpCircle,
  Lightbulb,
  Star,
  Calendar,
  MapPin,
  Video
} from 'lucide-react'
import { formatArabicNumber, formatArabicDate } from '../../lib/utils'

// Mock user data
const mockUser = {
  name: 'أحمد محمد',
  level: 3,
  xp: 2150,
  avatar: '/avatar.jpg'
}

// Mock community data
const communityData = {
  stats: {
    totalMembers: 15420,
    activeToday: 1250,
    totalPosts: 8930,
    studyGroups: 45
  },
  posts: [
    {
      id: 1,
      author: {
        name: 'فا<PERSON><PERSON>ة أحمد',
        level: 4,
        avatar: '/avatar1.jpg',
        badge: 'خبير المحاسبة'
      },
      title: 'كيفية حساب الاستهلاك بطريقة القسط الثابت؟',
      content: 'أحتاج مساعدة في فهم طريقة حساب الاستهلاك بالقسط الثابت. هل يمكن لأحد أن يشرح لي الخطوات بالتفصيل مع مثال عملي؟',
      category: 'أسئلة وأجوبة',
      tags: ['محاسبة', 'استهلاك', 'أصول ثابتة'],
      timestamp: new Date('2025-07-29T14:30:00'),
      likes: 24,
      replies: 8,
      views: 156,
      type: 'question'
    },
    {
      id: 2,
      author: {
        name: 'محمد علي',
        level: 5,
        avatar: '/avatar2.jpg',
        badge: 'معلم معتمد'
      },
      title: 'شرح مبسط للنسب المالية الأساسية',
      content: 'مشاركة ملخص شامل للنسب المالية الأساسية مع أمثلة عملية من السوق الجزائري. يتضمن نسب السيولة، الربحية، والنشاط.',
      category: 'مشاركة المعرفة',
      tags: ['تحليل مالي', 'نسب مالية', 'تقييم الشركات'],
      timestamp: new Date('2025-07-29T10:15:00'),
      likes: 89,
      replies: 15,
      views: 432,
      type: 'knowledge',
      hasAttachment: true
    },
    {
      id: 3,
      author: {
        name: 'سارة بن علي',
        level: 2,
        avatar: '/avatar3.jpg',
        badge: 'طالبة نشطة'
      },
      title: 'مجموعة دراسة للتحضير لامتحان SCF',
      content: 'أبحث عن زملاء للانضمام لمجموعة دراسة للتحضير لامتحان النظام المحاسبي المالي. سنلتقي أسبوعياً عبر الإنترنت.',
      category: 'مجموعات الدراسة',
      tags: ['SCF', 'مجموعة دراسة', 'امتحانات'],
      timestamp: new Date('2025-07-29T08:45:00'),
      likes: 12,
      replies: 6,
      views: 89,
      type: 'study-group'
    },
    {
      id: 4,
      author: {
        name: 'عبد الرحمن قاسم',
        level: 3,
        avatar: '/avatar4.jpg',
        badge: 'محلل مالي'
      },
      title: 'تحليل الأداء المالي لشركة سوناطراك 2024',
      content: 'تحليل شامل للأداء المالي لشركة سوناطراك للعام 2024، مع مقارنة بالسنوات السابقة وتوقعات المستقبل.',
      category: 'تحليلات وأبحاث',
      tags: ['تحليل مالي', 'سوناطراك', 'قطاع الطاقة'],
      timestamp: new Date('2025-07-28T16:20:00'),
      likes: 67,
      replies: 23,
      views: 298,
      type: 'analysis',
      hasAttachment: true
    }
  ],
  studyGroups: [
    {
      id: 1,
      name: 'مجموعة أساسيات المحاسبة',
      description: 'للمبتدئين في تعلم المحاسبة',
      members: 45,
      nextMeeting: new Date('2025-08-02T19:00:00'),
      category: 'مبتدئ',
      isOnline: true
    },
    {
      id: 2,
      name: 'نادي التحليل المالي',
      description: 'تحليل الشركات والأسواق المالية',
      members: 32,
      nextMeeting: new Date('2025-08-03T20:00:00'),
      category: 'متقدم',
      isOnline: true
    },
    {
      id: 3,
      name: 'ورشة SCF العملية',
      description: 'تطبيق النظام المحاسبي المالي',
      members: 28,
      nextMeeting: new Date('2025-08-05T18:30:00'),
      category: 'متوسط',
      isOnline: false,
      location: 'الجزائر العاصمة'
    }
  ],
  upcomingEvents: [
    {
      id: 1,
      title: 'ندوة: مستقبل المحاسبة الرقمية',
      date: new Date('2025-08-10T14:00:00'),
      speaker: 'د. أحمد بن محمد',
      attendees: 156,
      type: 'ندوة'
    },
    {
      id: 2,
      title: 'ورشة عملية: إعداد القوائم المالية',
      date: new Date('2025-08-12T16:00:00'),
      speaker: 'أ. فاطمة علي',
      attendees: 89,
      type: 'ورشة عمل'
    }
  ]
}

export default function CommunityPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<string>('recent')

  const { stats, posts, studyGroups, upcomingEvents } = communityData

  // Filter posts based on selected filters
  const filteredPosts = posts.filter(post => {
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.content.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  // Sort posts
  const sortedPosts = [...filteredPosts].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.likes - a.likes
      case 'replies':
        return b.replies - a.replies
      case 'recent':
      default:
        return b.timestamp.getTime() - a.timestamp.getTime()
    }
  })

  const getPostIcon = (type: string) => {
    switch (type) {
      case 'question':
        return <HelpCircle className="h-4 w-4 text-blue-600" />
      case 'knowledge':
        return <Lightbulb className="h-4 w-4 text-yellow-600" />
      case 'study-group':
        return <Users className="h-4 w-4 text-green-600" />
      case 'analysis':
        return <BookOpen className="h-4 w-4 text-purple-600" />
      default:
        return <MessageCircle className="h-4 w-4 text-gray-600" />
    }
  }

  const categories = [
    'أسئلة وأجوبة',
    'مشاركة المعرفة',
    'مجموعات الدراسة',
    'تحليلات وأبحاث',
    'أخبار ومستجدات'
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Header user={mockUser} />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3">
            <Users className="h-8 w-8 text-primary-600" />
            مجتمع التعلم
          </h1>
          <p className="text-gray-600">
            تواصل مع زملائك، شارك المعرفة، واحصل على المساعدة
          </p>
        </div>

        {/* Community Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-2xl font-bold text-primary-600 arabic-numbers">
                {formatArabicNumber(stats.totalMembers)}
              </div>
              <div className="text-sm text-gray-600">إجمالي الأعضاء</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-2xl font-bold text-green-600 arabic-numbers">
                {formatArabicNumber(stats.activeToday)}
              </div>
              <div className="text-sm text-gray-600">نشط اليوم</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-2xl font-bold text-blue-600 arabic-numbers">
                {formatArabicNumber(stats.totalPosts)}
              </div>
              <div className="text-sm text-gray-600">إجمالي المنشورات</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-2xl font-bold text-purple-600 arabic-numbers">
                {formatArabicNumber(stats.studyGroups)}
              </div>
              <div className="text-sm text-gray-600">مجموعات الدراسة</div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Create Post */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary-100 text-primary-700">
                    <Users className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <input
                      type="text"
                      placeholder="شارك سؤالاً أو معرفة مع المجتمع..."
                      className="w-full p-3 border rounded-lg text-right"
                    />
                  </div>
                  <Button>
                    <Plus className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                    نشر
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Filters */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col md:flex-row gap-4">
                  {/* Search */}
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute right-3 rtl:left-3 rtl:right-auto top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <input
                        type="text"
                        placeholder="ابحث في المنشورات..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 rtl:pr-10 rtl:pl-3 pr-3 py-2 border rounded-md text-right"
                      />
                    </div>
                  </div>

                  {/* Category Filter */}
                  <div className="md:w-48">
                    <select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="w-full p-2 border rounded-md text-right"
                    >
                      <option value="all">جميع الفئات</option>
                      {categories.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>

                  {/* Sort */}
                  <div className="md:w-32">
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      className="w-full p-2 border rounded-md text-right"
                    >
                      <option value="recent">الأحدث</option>
                      <option value="popular">الأكثر إعجاباً</option>
                      <option value="replies">الأكثر تفاعلاً</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Posts */}
            <div className="space-y-4">
              {sortedPosts.map((post) => (
                <Card key={post.id} className="card-hover">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100">
                          <Users className="h-5 w-5 text-gray-600" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{post.author.name}</span>
                            <Badge variant="outline" className="text-xs">
                              المستوى {post.author.level}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {post.author.badge}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-500">
                            <Clock className="h-3 w-3" />
                            {formatArabicDate(post.timestamp)}
                            <Badge variant="outline" className="text-xs">
                              {post.category}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      {getPostIcon(post.type)}
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div>
                      <h3 className="font-semibold text-lg mb-2">{post.title}</h3>
                      <p className="text-gray-700 leading-relaxed">{post.content}</p>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2">
                      {post.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          #{tag}
                        </Badge>
                      ))}
                    </div>

                    {/* Attachment */}
                    {post.hasAttachment && (
                      <div className="p-3 bg-gray-50 rounded-lg flex items-center gap-2">
                        <BookOpen className="h-4 w-4 text-gray-600" />
                        <span className="text-sm text-gray-700">مرفق: ملف PDF</span>
                        <Button variant="outline" size="sm">تحميل</Button>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center gap-4">
                        <Button variant="ghost" size="sm" className="flex items-center gap-1">
                          <ThumbsUp className="h-4 w-4" />
                          <span className="arabic-numbers">{formatArabicNumber(post.likes)}</span>
                        </Button>
                        <Button variant="ghost" size="sm" className="flex items-center gap-1">
                          <MessageCircle className="h-4 w-4" />
                          <span className="arabic-numbers">{formatArabicNumber(post.replies)}</span>
                        </Button>
                        <Button variant="ghost" size="sm" className="flex items-center gap-1">
                          <Share2 className="h-4 w-4" />
                          مشاركة
                        </Button>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <Eye className="h-3 w-3" />
                        <span className="arabic-numbers">{formatArabicNumber(post.views)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Study Groups */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  مجموعات الدراسة
                </CardTitle>
                <CardDescription>
                  انضم لمجموعات الدراسة النشطة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {studyGroups.map((group) => (
                  <div key={group.id} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-sm">{group.name}</h3>
                      <Badge variant="outline" className="text-xs">
                        {group.category}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 mb-2">{group.description}</p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {formatArabicNumber(group.members)} عضو
                      </span>
                      <span className="flex items-center gap-1">
                        {group.isOnline ? (
                          <Video className="h-3 w-3 text-green-600" />
                        ) : (
                          <MapPin className="h-3 w-3 text-blue-600" />
                        )}
                        {group.isOnline ? 'عبر الإنترنت' : group.location}
                      </span>
                    </div>
                    <div className="mt-2 pt-2 border-t">
                      <div className="text-xs text-gray-600 mb-1">الاجتماع القادم:</div>
                      <div className="text-xs font-medium">
                        {formatArabicDate(group.nextMeeting)}
                      </div>
                    </div>
                    <Button variant="outline" size="sm" className="w-full mt-2">
                      انضم للمجموعة
                    </Button>
                  </div>
                ))}
                <Button variant="outline" className="w-full">
                  عرض جميع المجموعات
                </Button>
              </CardContent>
            </Card>

            {/* Upcoming Events */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  الأحداث القادمة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="p-3 border rounded-lg">
                    <h3 className="font-medium text-sm mb-1">{event.title}</h3>
                    <div className="text-xs text-gray-600 mb-2">
                      المتحدث: {event.speaker}
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{formatArabicDate(event.date)}</span>
                      <span className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {formatArabicNumber(event.attendees)}
                      </span>
                    </div>
                    <Badge variant="outline" className="text-xs mt-2">
                      {event.type}
                    </Badge>
                    <Button variant="outline" size="sm" className="w-full mt-2">
                      سجل حضورك
                    </Button>
                  </div>
                ))}
                <Button variant="outline" className="w-full">
                  عرض جميع الأحداث
                </Button>
              </CardContent>
            </Card>

            {/* Top Contributors */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5" />
                  أبرز المساهمين
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {[
                  { name: 'د. أحمد محمد', points: 2450, badge: 'خبير محاسبة' },
                  { name: 'أ. فاطمة علي', points: 1890, badge: 'محلل مالي' },
                  { name: 'محمد قاسم', points: 1650, badge: 'مدقق معتمد' }
                ].map((contributor, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700 text-sm font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{contributor.name}</div>
                        <div className="text-xs text-gray-600">{contributor.badge}</div>
                      </div>
                    </div>
                    <div className="text-sm font-bold text-primary-600 arabic-numbers">
                      {formatArabicNumber(contributor.points)}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
