import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Arabic number formatting utilities
export function formatArabicNumber(num: number): string {
  return new Intl.NumberFormat('ar-DZ', {
    useGrouping: true,
  }).format(num)
}

export function formatCurrency(amount: number, currency: string = 'DZD'): string {
  return new Intl.NumberFormat('ar-DZ', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
  }).format(amount)
}

export function formatPercentage(value: number): string {
  return `${value.toFixed(2)}%`
}

// Date formatting for Arabic locale
export function formatArabicDate(date: Date): string {
  return new Intl.DateTimeFormat('ar-DZ', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}

// Learning progress utilities
export function calculateProgress(completed: number, total: number): number {
  return Math.round((completed / total) * 100)
}

export function getProgressColor(progress: number): string {
  if (progress >= 80) return 'text-green-600'
  if (progress >= 60) return 'text-blue-600'
  if (progress >= 40) return 'text-yellow-600'
  return 'text-red-600'
}

// XP and level calculations
export function calculateLevel(xp: number): number {
  if (xp >= 5000) return 5 // خبير
  if (xp >= 3000) return 4 // محترف
  if (xp >= 1500) return 3 // ممارس
  if (xp >= 500) return 2 // متدرب
  return 1 // مبتدئ
}

export function getLevelName(level: number): string {
  const levels = {
    1: 'مبتدئ',
    2: 'متدرب',
    3: 'ممارس',
    4: 'محترف',
    5: 'خبير'
  }
  return levels[level as keyof typeof levels] || 'مبتدئ'
}

export function getXPForNextLevel(currentXP: number): number {
  const thresholds = [0, 500, 1500, 3000, 5000]
  const currentLevel = calculateLevel(currentXP)
  
  if (currentLevel >= 5) return 0 // Max level reached
  
  return thresholds[currentLevel] - currentXP
}

// Time formatting
export function formatStudyTime(minutes: number): string {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  
  if (hours > 0) {
    return `${hours} ساعة و ${mins} دقيقة`
  }
  return `${mins} دقيقة`
}

// Validation utilities
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isValidPhoneNumber(phone: string): boolean {
  // Algerian phone number format
  const phoneRegex = /^(\+213|0)(5|6|7)[0-9]{8}$/
  return phoneRegex.test(phone)
}

// Content difficulty helpers
export function getDifficultyColor(difficulty: 'مبتدئ' | 'متوسط' | 'متقدم'): string {
  switch (difficulty) {
    case 'مبتدئ':
      return 'bg-green-100 text-green-800'
    case 'متوسط':
      return 'bg-yellow-100 text-yellow-800'
    case 'متقدم':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Quiz scoring
export function calculateQuizScore(correct: number, total: number): {
  score: number
  grade: string
  color: string
} {
  const percentage = (correct / total) * 100
  
  let grade = 'راسب'
  let color = 'text-red-600'
  
  if (percentage >= 90) {
    grade = 'ممتاز'
    color = 'text-green-600'
  } else if (percentage >= 80) {
    grade = 'جيد جداً'
    color = 'text-blue-600'
  } else if (percentage >= 70) {
    grade = 'جيد'
    color = 'text-yellow-600'
  } else if (percentage >= 60) {
    grade = 'مقبول'
    color = 'text-orange-600'
  }
  
  return { score: percentage, grade, color }
}
