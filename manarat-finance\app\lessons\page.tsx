'use client'

import React, { useState } from 'react'
import { Header } from '../../components/layout/header'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import {
  BookOpen,
  PlayCircle,
  Clock,
  Star,
  CheckCircle,
  Lock,
  Filter,
  Search,
  TrendingUp,
  Award,
  Users,
  FileText
} from 'lucide-react'
import { formatArabicNumber, getDifficultyColor, calculateProgress } from '../../lib/utils'

// Mock user data
const mockUser = {
  name: 'أحمد محمد',
  level: 3,
  xp: 2150,
  avatar: '/avatar.jpg'
}

// Mock lessons data
const lessonsData = {
  categories: [
    {
      id: 'accounting-basics',
      title: 'أساسيات المحاسبة',
      description: 'تعلم المبادئ الأساسية للمحاسبة المالية',
      icon: '📊',
      lessons: 15,
      completed: 12,
      color: 'bg-blue-500'
    },
    {
      id: 'financial-analysis',
      title: 'التحليل المالي',
      description: 'تحليل القوائم المالية والنسب المالية',
      icon: '📈',
      lessons: 12,
      completed: 8,
      color: 'bg-green-500'
    },
    {
      id: 'scf-system',
      title: 'النظام المحاسبي المالي SCF',
      description: 'النظام المحاسبي المالي الجزائري',
      icon: '🏛️',
      lessons: 10,
      completed: 3,
      color: 'bg-purple-500'
    },
    {
      id: 'islamic-finance',
      title: 'التمويل الإسلامي',
      description: 'مبادئ وأدوات التمويل الإسلامي',
      icon: '🕌',
      lessons: 8,
      completed: 0,
      color: 'bg-orange-500'
    }
  ],
  recentLessons: [
    {
      id: 1,
      title: 'مقدمة في المحاسبة المالية',
      category: 'أساسيات المحاسبة',
      duration: '45 دقيقة',
      difficulty: 'مبتدئ' as const,
      progress: 100,
      completed: true,
      rating: 4.8,
      students: 1250,
      description: 'تعرف على المفاهيم الأساسية للمحاسبة المالية ودورها في الأعمال'
    },
    {
      id: 2,
      title: 'دورة المحاسبة والقيود',
      category: 'أساسيات المحاسبة',
      duration: '60 دقيقة',
      difficulty: 'مبتدئ' as const,
      progress: 85,
      completed: false,
      rating: 4.9,
      students: 980,
      description: 'فهم دورة المحاسبة وكيفية تسجيل القيود المحاسبية'
    },
    {
      id: 3,
      title: 'إعداد الميزانية العمومية',
      category: 'أساسيات المحاسبة',
      duration: '75 دقيقة',
      difficulty: 'متوسط' as const,
      progress: 30,
      completed: false,
      rating: 4.7,
      students: 750,
      description: 'تعلم كيفية إعداد الميزانية العمومية وفهم مكوناتها'
    },
    {
      id: 4,
      title: 'تحليل النسب المالية',
      category: 'التحليل المالي',
      duration: '90 دقيقة',
      difficulty: 'متوسط' as const,
      progress: 0,
      completed: false,
      rating: 4.6,
      students: 650,
      description: 'استخدام النسب المالية لتقييم الأداء المالي للشركات'
    },
    {
      id: 5,
      title: 'مبادئ SCF الأساسية',
      category: 'النظام المحاسبي المالي SCF',
      duration: '120 دقيقة',
      difficulty: 'متقدم' as const,
      progress: 0,
      completed: false,
      rating: 4.5,
      students: 420,
      description: 'فهم المبادئ الأساسية للنظام المحاسبي المالي الجزائري',
      locked: true
    },
    {
      id: 6,
      title: 'أساسيات التمويل الإسلامي',
      category: 'التمويل الإسلامي',
      duration: '100 دقيقة',
      difficulty: 'متوسط' as const,
      progress: 0,
      completed: false,
      rating: 4.4,
      students: 380,
      description: 'المبادئ الشرعية للتمويل الإسلامي وأدواته المختلفة',
      locked: true
    }
  ]
}

export default function LessonsPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [difficultyFilter, setDifficultyFilter] = useState<string>('all')

  const { categories, recentLessons } = lessonsData

  // Filter lessons based on selected filters
  const filteredLessons = recentLessons.filter(lesson => {
    const matchesCategory = selectedCategory === 'all' || lesson.category === categories.find(cat => cat.id === selectedCategory)?.title
    const matchesSearch = lesson.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lesson.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDifficulty = difficultyFilter === 'all' || lesson.difficulty === difficultyFilter

    return matchesCategory && matchesSearch && matchesDifficulty
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <Header user={mockUser} />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3">
            <BookOpen className="h-8 w-8 text-primary-600" />
            الدروس التعليمية
          </h1>
          <p className="text-gray-600">
            اكتشف مجموعة شاملة من الدروس في المحاسبة والمالية
          </p>
        </div>

        {/* Categories Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {categories.map((category) => {
            const progress = calculateProgress(category.completed, category.lessons)
            return (
              <Card
                key={category.id}
                className={`card-hover cursor-pointer ${selectedCategory === category.id ? 'ring-2 ring-primary-500' : ''}`}
                onClick={() => setSelectedCategory(selectedCategory === category.id ? 'all' : category.id)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="text-3xl">{category.icon}</div>
                    <Badge variant="outline" className="text-xs">
                      {formatArabicNumber(category.lessons)} درس
                    </Badge>
                  </div>
                  <CardTitle className="text-lg">{category.title}</CardTitle>
                  <CardDescription className="text-sm">
                    {category.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>التقدم</span>
                      <span className="arabic-numbers">{formatArabicNumber(category.completed)}/{formatArabicNumber(category.lessons)}</span>
                    </div>
                    <Progress value={progress} />
                    <div className="text-xs text-gray-500 text-center">
                      {progress}% مكتمل
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 rtl:left-3 rtl:right-auto top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="ابحث في الدروس..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 rtl:pr-10 rtl:pl-3 pr-3 py-2 border rounded-md text-right"
                  />
                </div>
              </div>

              {/* Difficulty Filter */}
              <div className="md:w-48">
                <select
                  value={difficultyFilter}
                  onChange={(e) => setDifficultyFilter(e.target.value)}
                  className="w-full p-2 border rounded-md text-right"
                >
                  <option value="all">جميع المستويات</option>
                  <option value="مبتدئ">مبتدئ</option>
                  <option value="متوسط">متوسط</option>
                  <option value="متقدم">متقدم</option>
                </select>
              </div>

              {/* Clear Filters */}
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedCategory('all')
                  setSearchTerm('')
                  setDifficultyFilter('all')
                }}
              >
                <Filter className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                مسح الفلاتر
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Lessons Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredLessons.map((lesson) => (
            <Card key={lesson.id} className="card-hover">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <CardTitle className="text-lg">{lesson.title}</CardTitle>
                      {lesson.completed && <CheckCircle className="h-5 w-5 text-green-600" />}
                      {lesson.locked && <Lock className="h-5 w-5 text-gray-400" />}
                    </div>
                    <Badge variant="outline" className="text-xs mb-2">
                      {lesson.category}
                    </Badge>
                    <CardDescription className="text-sm">
                      {lesson.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Lesson Stats */}
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {lesson.duration}
                    </span>
                    <span className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {formatArabicNumber(lesson.students)}
                    </span>
                    <span className="flex items-center gap-1">
                      <Star className="h-3 w-3 text-yellow-500" />
                      {lesson.rating}
                    </span>
                  </div>
                  <Badge
                    className={getDifficultyColor(lesson.difficulty)}
                  >
                    {lesson.difficulty}
                  </Badge>
                </div>

                {/* Progress Bar */}
                {lesson.progress > 0 && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>التقدم</span>
                      <span className="arabic-numbers">{lesson.progress}%</span>
                    </div>
                    <Progress value={lesson.progress} />
                  </div>
                )}

                {/* Action Button */}
                <div className="flex gap-2">
                  <Button
                    className="flex-1"
                    variant={lesson.completed ? "outline" : "default"}
                    disabled={lesson.locked}
                  >
                    {lesson.locked ? (
                      <>
                        <Lock className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                        مقفل
                      </>
                    ) : lesson.completed ? (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                        مراجعة
                      </>
                    ) : lesson.progress > 0 ? (
                      <>
                        <PlayCircle className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                        متابعة
                      </>
                    ) : (
                      <>
                        <PlayCircle className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                        بدء الدرس
                      </>
                    )}
                  </Button>

                  {!lesson.locked && (
                    <Button variant="outline" size="icon">
                      <FileText className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredLessons.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                لا توجد دروس مطابقة
              </h3>
              <p className="text-gray-600 mb-4">
                جرب تغيير معايير البحث أو الفلاتر
              </p>
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedCategory('all')
                  setSearchTerm('')
                  setDifficultyFilter('all')
                }}
              >
                مسح جميع الفلاتر
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Learning Path Suggestion */}
        <Card className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary-600" />
              مسار التعلم المقترح
            </CardTitle>
            <CardDescription>
              بناءً على مستواك الحالي، نقترح عليك هذا المسار التعليمي
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-white rounded-lg">
                <div className="text-2xl mb-2">📚</div>
                <h3 className="font-semibold mb-1">المرحلة الحالية</h3>
                <p className="text-sm text-gray-600">أساسيات المحاسبة</p>
                <Badge variant="success" className="mt-2">80% مكتمل</Badge>
              </div>
              <div className="text-center p-4 bg-white rounded-lg">
                <div className="text-2xl mb-2">📊</div>
                <h3 className="font-semibold mb-1">المرحلة التالية</h3>
                <p className="text-sm text-gray-600">التحليل المالي</p>
                <Badge variant="warning" className="mt-2">67% مكتمل</Badge>
              </div>
              <div className="text-center p-4 bg-white rounded-lg">
                <div className="text-2xl mb-2">🏛️</div>
                <h3 className="font-semibold mb-1">الهدف القادم</h3>
                <p className="text-sm text-gray-600">النظام المحاسبي SCF</p>
                <Badge variant="outline" className="mt-2">30% مكتمل</Badge>
              </div>
            </div>
            <div className="text-center mt-6">
              <Button>
                <Award className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                عرض المسار الكامل
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
