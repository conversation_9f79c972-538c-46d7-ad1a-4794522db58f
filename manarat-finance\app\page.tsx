'use client'

import React from 'react'
import { Header } from '../components/layout/header'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { Progress } from '../components/ui/progress'
import {
  BookOpen,
  Calculator,
  Users,
  Trophy,
  TrendingUp,
  Clock,
  Target,
  Award,
  PlayCircle,
  BarChart3,
  Lightbulb,
  Star,
  Calendar,
  CheckCircle
} from 'lucide-react'
import { formatCurrency, formatArabicNumber, getLevelName, calculateProgress } from '../lib/utils'

// Mock user data
const mockUser = {
  name: 'أحمد محمد',
  level: 3,
  xp: 2150,
  avatar: '/avatar.jpg'
}

// Mock dashboard data
const dashboardData = {
  stats: {
    completedLessons: 45,
    totalLessons: 120,
    studyStreak: 12,
    totalXP: 2150,
    weeklyGoal: 5,
    completedThisWeek: 3
  },
  recentLessons: [
    {
      id: 1,
      title: 'أساسيات المحاسبة المالية',
      progress: 85,
      duration: '45 دقيقة',
      difficulty: 'مبتدئ' as const,
      completed: false
    },
    {
      id: 2,
      title: 'تحليل القوائم المالية',
      progress: 100,
      duration: '60 دقيقة',
      difficulty: 'متوسط' as const,
      completed: true
    },
    {
      id: 3,
      title: 'النظام المحاسبي المالي SCF',
      progress: 30,
      duration: '90 دقيقة',
      difficulty: 'متقدم' as const,
      completed: false
    }
  ],
  achievements: [
    { id: 1, name: 'محاسب مبتدئ', description: 'أكمل 10 دروس', earned: true, icon: '🏆' },
    { id: 2, name: 'خبير التحليل', description: 'حل 50 مسألة تحليل مالي', earned: true, icon: '📊' },
    { id: 3, name: 'نجم الأسبوع', description: 'الأول في التحديات الأسبوعية', earned: false, icon: '⭐' },
    { id: 4, name: 'ماراثون التعلم', description: '30 يوم متواصل', earned: false, icon: '🔥' }
  ],
  upcomingEvents: [
    {
      id: 1,
      title: 'ورشة عمل: التمويل الإسلامي',
      date: '2025-08-05',
      time: '14:00',
      type: 'ورشة عمل'
    },
    {
      id: 2,
      title: 'امتحان تجريبي: المحاسبة العامة',
      date: '2025-08-08',
      time: '10:00',
      type: 'امتحان'
    }
  ]
}

export default function Home() {
  const { stats, recentLessons, achievements, upcomingEvents } = dashboardData
  const overallProgress = calculateProgress(stats.completedLessons, stats.totalLessons)
  const weeklyProgress = calculateProgress(stats.completedThisWeek, stats.weeklyGoal)

  return (
    <div className="min-h-screen bg-gray-50">
      <Header user={mockUser} />

      <main className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            مرحباً بك، {mockUser.name}! 👋
          </h1>
          <p className="text-gray-600">
            استمر في رحلتك التعليمية في عالم المحاسبة والمالية
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي النقاط</CardTitle>
              <Trophy className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold arabic-numbers">{formatArabicNumber(stats.totalXP)}</div>
              <p className="text-xs text-muted-foreground">
                المستوى {mockUser.level} - {getLevelName(mockUser.level)}
              </p>
            </CardContent>
          </Card>

          <Card className="card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الدروس المكتملة</CardTitle>
              <BookOpen className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold arabic-numbers">
                {formatArabicNumber(stats.completedLessons)}/{formatArabicNumber(stats.totalLessons)}
              </div>
              <Progress value={overallProgress} className="mt-2" />
              <p className="text-xs text-muted-foreground mt-1">
                {overallProgress}% مكتمل
              </p>
            </CardContent>
          </Card>

          <Card className="card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">سلسلة الدراسة</CardTitle>
              <Target className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold arabic-numbers">{formatArabicNumber(stats.studyStreak)}</div>
              <p className="text-xs text-muted-foreground">
                يوم متواصل 🔥
              </p>
            </CardContent>
          </Card>

          <Card className="card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الهدف الأسبوعي</CardTitle>
              <Calendar className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold arabic-numbers">
                {formatArabicNumber(stats.completedThisWeek)}/{formatArabicNumber(stats.weeklyGoal)}
              </div>
              <Progress value={weeklyProgress} className="mt-2" />
              <p className="text-xs text-muted-foreground mt-1">
                {weeklyProgress}% من الهدف
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Lessons */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  الدروس الحديثة
                </CardTitle>
                <CardDescription>
                  استكمل دروسك واكسب المزيد من النقاط
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentLessons.map((lesson) => (
                  <div key={lesson.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium">{lesson.title}</h3>
                        {lesson.completed && <CheckCircle className="h-4 w-4 text-green-600" />}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {lesson.duration}
                        </span>
                        <Badge
                          variant={lesson.difficulty === 'مبتدئ' ? 'success' : lesson.difficulty === 'متوسط' ? 'warning' : 'destructive'}
                          className="text-xs"
                        >
                          {lesson.difficulty}
                        </Badge>
                      </div>
                      <Progress value={lesson.progress} className="mt-2" />
                    </div>
                    <Button
                      variant={lesson.completed ? "outline" : "default"}
                      size="sm"
                      className="mr-4 rtl:ml-4 rtl:mr-0"
                    >
                      {lesson.completed ? 'مراجعة' : 'متابعة'}
                      <PlayCircle className="h-4 w-4 mr-1 rtl:ml-1 rtl:mr-0" />
                    </Button>
                  </div>
                ))}
                <Button variant="outline" className="w-full">
                  عرض جميع الدروس
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Achievements */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5" />
                  الإنجازات
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {achievements.slice(0, 4).map((achievement) => (
                  <div
                    key={achievement.id}
                    className={`flex items-center gap-3 p-2 rounded-lg ${
                      achievement.earned ? 'bg-green-50 border border-green-200' : 'bg-gray-50'
                    }`}
                  >
                    <div className="text-2xl">{achievement.icon}</div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{achievement.name}</div>
                      <div className="text-xs text-gray-600">{achievement.description}</div>
                    </div>
                    {achievement.earned && <CheckCircle className="h-4 w-4 text-green-600" />}
                  </div>
                ))}
                <Button variant="outline" size="sm" className="w-full">
                  عرض جميع الإنجازات
                </Button>
              </CardContent>
            </Card>

            {/* Upcoming Events */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  الأحداث القادمة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="p-3 border rounded-lg">
                    <div className="font-medium text-sm mb-1">{event.title}</div>
                    <div className="text-xs text-gray-600 mb-2">
                      {event.date} في {event.time}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {event.type}
                    </Badge>
                  </div>
                ))}
                <Button variant="outline" size="sm" className="w-full">
                  عرض التقويم الكامل
                </Button>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5" />
                  إجراءات سريعة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Calculator className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  الحاسبة المالية
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <BarChart3 className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  محاكي البورصة
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                  مجموعات الدراسة
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
